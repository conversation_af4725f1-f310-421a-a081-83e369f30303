import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import CardSwap, { Card } from './CardSwap';
import projects from '../data/projects';

const FeaturedSection = styled.section`
  padding: 0;
  position: relative;
  margin-top: -8rem;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding-right: 15%;
  min-height: 100vh;

  @media (max-width: 768px) {
    padding: 0;
    margin-top: -250px;
    margin-left: -150px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 90vh;
    height: auto;
  }
`;

const ProjectIntro = styled.div`
  position: absolute;
  left: 15%;
  top: 50%;
  transform: translateY(-50%);
  writing-mode: vertical-lr;
  text-orientation: mixed;
  font-size: 1.1rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  color: ${({ theme }) => theme.text};
  z-index: 1;

  @media (max-width: 768px) {
    display: none;
  }
`;

const VerticalTitle = styled.h2`
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  background: linear-gradient(
    to bottom,
    ${({ theme }) => theme.gradientStart},
    ${({ theme }) => theme.gradientEnd}
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;

  @media (max-width: 768px) {
    font-size: 1.8rem;
    margin: 0;
  }
`;

const VerticalText = styled.p`
  line-height: 1.6;
  letter-spacing: 0.1em;
  color: ${({ theme }) => theme.text};

  @media (max-width: 768px) {
    writing-mode: horizontal-tb;
    text-align: left;
    font-size: 1rem;
    margin: 0;
    padding: 0;
    flex: 1;
    br {
      display: none;
    }
  }
`;

const ContentWrapper = styled.div`
  max-width: 1200px;
  padding: 0 2rem;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  position: relative;
  width: 100%;

  @media (max-width: 768px) {
    padding: 0;
    margin: 0;
    align-items: center;
    margin-left: 1rem;
  }
`;

const CardContainer = styled.div`
  height: 650px;
  position: relative;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-bottom: 4rem;

  @media (max-width: 768px) {
    height: 605px;
    margin: 0;
    padding: 0;
    justify-content: center;
    max-width: 495px;
    transform: translateY(10px);
  }
`;

const HoverTip = styled.div`
  writing-mode: vertical-lr;
  text-orientation: mixed;
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textSecondary || '#888'};
  opacity: 0.8;
  transition: all 0.3s ease;
  pointer-events: none;
  font-style: italic;
  margin-top: 1rem;
  letter-spacing: 0.1em;
  line-height: 1.6;

  @media (max-width: 768px) {
    display: none;
  }
`;

const ProjectCard = styled(Card)`
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  overflow: hidden;
  height: 100%;
  border: 1px solid ${({ theme }) => theme.border};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }
`;

const ImageContainer = styled.div`
  position: relative;
  overflow: hidden;
  aspect-ratio: 16 / 12;
  max-height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f5fb;
  border-bottom: 1px solid #eaeaea;
`;

const Image = styled.img`
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  padding: 10px;
`;

const Content = styled.div`
  padding: 1.8rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
`;

const TitleContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
`;

const Title = styled.h3`
  font-size: 1.4rem;
  color: #333;
  margin: 0;
  line-height: 1.3;
  flex: 1;
`;

const LinksContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex-shrink: 0;
`;

const LinkButton = styled.a`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  background: linear-gradient(135deg, #AED6F1 0%, #85C1E9 100%);
  color: #2C3E50;
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
  white-space: nowrap;
  
  &:hover {
    background: linear-gradient(135deg, #5DADE2 0%, #3498DB 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(52, 152, 219, 0.3);
  }
  
  &.demo {
    background: linear-gradient(135deg, #A9DFBF 0%, #82E0AA 100%);
    
    &:hover {
      background: linear-gradient(135deg, #58D68D 0%, #28B463 100%);
      color: white;
      box-shadow: 0 3px 8px rgba(40, 180, 99, 0.3);
    }
  }

  &.detail {
    background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
    
    &:hover {
      background: linear-gradient(135deg, #BA68C8 0%, #9C27B0 100%);
      color: white;
      box-shadow: 0 3px 8px rgba(156, 39, 176, 0.3);
    }
  }
`;

const LinkIcon = styled.span`
  font-size: 0.8rem;
`;

const ProjectDescription = styled.p`
  font-size: 0.95rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  
  strong {
    font-weight: bold;
    color: ${({ theme }) => theme.primary};
  }
`;

const TechStack = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin-top: auto;
`;

const TechTag = styled.span`
  background-color: #f0f7ff;
  color: #4a8eff;
  font-size: 0.85rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
`;

// 高亮描述文本中的关键词
const highlightText = (text, terms) => {
  if (!terms || terms.length === 0) return text;

  let result = text;
  const sortedTerms = [...terms].sort((a, b) => b.length - a.length);

  sortedTerms.forEach(term => {
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    let regex;

    if (/\d+%$/.test(term)) {
      regex = new RegExp(escapedTerm, 'g');
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
    else if (/\d+\+$/.test(term)) {
      regex = new RegExp(escapedTerm, 'g');
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
    else if (/^\d+$/.test(term)) {
      regex = new RegExp(`(^|[^\\d])${escapedTerm}([^\\d]|$)`, 'g');
      result = result.replace(regex, (match, p1, p2) => `${p1}<strong>${term}</strong>${p2}`);
      return;
    }
    else if (/[\u4e00-\u9fa5]/.test(term)) {
      regex = new RegExp(escapedTerm, 'g');
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
    else {
      regex = new RegExp(`\\b${escapedTerm}\\b`, 'g');
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
  });

  return result;
};

const FeaturedProjects = () => {
  const navigate = useNavigate();
  const featuredProjects = projects.slice(0, 5);

  return (
    <FeaturedSection id="projects">
      <ProjectIntro>
        <VerticalTitle>项目集</VerticalTitle>
        <VerticalText>
          这里展示了我的一些个人项目
          每个项目都代表了不同阶段的成长
        </VerticalText>
        <HoverTip>
          悬停可暂停切换
        </HoverTip>
      </ProjectIntro>
      <ContentWrapper>
        <CardContainer>
          <CardSwap
            width={window.innerWidth <= 768 ? 495 : 450}
            height={window.innerWidth <= 768 ? 572 : 520}
            cardDistance={window.innerWidth <= 768 ? 44 : 40}
            verticalDistance={window.innerWidth <= 768 ? 44 : 40}
            delay={1500}
            pauseOnHover={true}
            easing="elastic"
          >
            {featuredProjects.map(project => {
              const highlightedDescription = project.highlightTerms
                ? highlightText(project.description, project.highlightTerms)
                : project.description;

              return (
                <ProjectCard key={project.id}>
                  <ImageContainer>
                    <Image src={project.image} alt={project.title} />
                  </ImageContainer>
                  <Content>
                    <TitleContainer>
                      <Title>{project.title}</Title>
                      {/* 项目链接区域 - 移动到标题右侧 */}
                      <LinksContainer>
                        {project.liveUrl && (
                          <LinkButton
                            href={project.liveUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <LinkIcon>🌐</LinkIcon>
                            访问
                          </LinkButton>
                        )}
                        {project.demoInfo && (
                          <LinkButton
                            className="demo"
                            onClick={(e) => {
                              e.preventDefault();
                              // 复制到剪贴板
                              if (navigator.clipboard) {
                                navigator.clipboard.writeText(project.demoInfo).then(() => {
                                  alert(`已复制到剪贴板：${project.demoInfo}`);
                                }).catch(() => {
                                  alert(`体验方式：${project.demoInfo}`);
                                });
                              } else {
                                alert(`体验方式：${project.demoInfo}`);
                              }
                            }}
                          >
                            <LinkIcon>📱</LinkIcon>
                            体验
                          </LinkButton>
                        )}
                        {project.id === 3 && (
                          <LinkButton
                            className="detail"
                            onClick={(e) => {
                              e.preventDefault();
                              navigate(`/project/${project.id}`);
                            }}
                          >
                            <LinkIcon>📋</LinkIcon>
                            详情
                          </LinkButton>
                        )}
                      </LinksContainer>
                    </TitleContainer>

                    <ProjectDescription
                      dangerouslySetInnerHTML={{ __html: highlightedDescription }}
                    />
                    <TechStack>
                      {project.techStack.map((tech, index) => (
                        <TechTag key={index}>{tech}</TechTag>
                      ))}
                    </TechStack>
                  </Content>
                </ProjectCard>
              );
            })}
          </CardSwap>
        </CardContainer>
      </ContentWrapper>
    </FeaturedSection>
  );
};

export default FeaturedProjects; 