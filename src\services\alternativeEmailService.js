// 备用邮件发送方案 - 使用Formspree或其他服务

/**
 * 使用Formspree发送邮件的备用方案
 * 访问 https://formspree.io/ 注册并获取表单端点
 */
export const sendEmailViaFormspree = async (formData) => {
  try {
    const response = await fetch('https://formspree.io/f/YOUR_FORM_ID', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
      }),
    });

    if (response.ok) {
      return {
        success: true,
        message: '邮件发送成功！我会尽快回复您。'
      };
    } else {
      throw new Error('Formspree发送失败');
    }
  } catch (error) {
    console.error('Formspree发送失败:', error);
    return {
      success: false,
      message: '邮件发送失败，请稍后重试。'
    };
  }
};

/**
 * 使用Netlify Forms的备用方案（如果部署在Netlify）
 */
export const sendEmailViaNetlify = async (formData) => {
  try {
    const formDataObj = new FormData();
    formDataObj.append('form-name', 'contact');
    formDataObj.append('name', formData.name);
    formDataObj.append('email', formData.email);
    formDataObj.append('subject', formData.subject);
    formDataObj.append('message', formData.message);

    const response = await fetch('/', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams(formDataObj).toString()
    });

    if (response.ok) {
      return {
        success: true,
        message: '邮件发送成功！我会尽快回复您。'
      };
    } else {
      throw new Error('Netlify Forms发送失败');
    }
  } catch (error) {
    console.error('Netlify Forms发送失败:', error);
    return {
      success: false,
      message: '邮件发送失败，请稍后重试。'
    };
  }
};

/**
 * 多重发送策略 - 依次尝试不同的发送方式
 */
export const sendEmailWithFallback = async (formData, primarySendFunction) => {
  // 首先尝试主要发送方式（EmailJS）
  try {
    const result = await primarySendFunction(formData);
    if (result.success) {
      return result;
    }
  } catch (error) {
    console.log('主要发送方式失败，尝试备用方案');
  }

  // 如果主要方式失败，尝试备用方案
  try {
    return await sendEmailViaFormspree(formData);
  } catch (error) {
    console.log('备用方案也失败');
    return {
      success: false,
      message: '所有邮件发送方式都失败了，请直接联系 <EMAIL>'
    };
  }
};
