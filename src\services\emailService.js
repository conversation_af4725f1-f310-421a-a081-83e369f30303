import emailjs from '@emailjs/browser';

// EmailJS配置 - 从环境变量读取
const EMAILJS_CONFIG = {
  SERVICE_ID: import.meta.env.VITE_EMAILJS_SERVICE_ID,
  TEMPLATE_ID: import.meta.env.VITE_EMAILJS_TEMPLATE_ID,
  PUBLIC_KEY: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
};

// 初始化EmailJS
emailjs.init(EMAILJS_CONFIG.PUBLIC_KEY);

/**
 * 发送邮件
 * @param {Object} formData - 表单数据
 * @param {string} formData.name - 发送者姓名
 * @param {string} formData.email - 发送者邮箱
 * @param {string} formData.subject - 邮件主题
 * @param {string} formData.message - 邮件内容
 * @returns {Promise} - 发送结果
 */
export const sendEmail = async (formData) => {
  try {
    // 准备模板参数
    const templateParams = {
      from_name: formData.name,
      from_email: formData.email,
      subject: formData.subject,
      message: formData.message,
      to_name: 'Tu<PERSON>', // 接收者姓名
      reply_to: formData.email,
    };

    // 发送邮件
    const response = await emailjs.send(
      EMAILJS_CONFIG.SERVICE_ID,
      EMAILJS_CONFIG.TEMPLATE_ID,
      templateParams
    );

    console.log('邮件发送成功:', response);
    return {
      success: true,
      message: '邮件发送成功！我会尽快回复您。',
      data: response
    };
  } catch (error) {
    console.error('邮件发送失败:', error);
    return {
      success: false,
      message: '邮件发送失败，请稍后重试或直接联系我。',
      error: error
    };
  }
};

/**
 * 验证表单数据
 * @param {Object} formData - 表单数据
 * @returns {Object} - 验证结果
 */
export const validateFormData = (formData) => {
  const errors = {};

  if (!formData.name || formData.name.trim().length < 2) {
    errors.name = '姓名至少需要2个字符';
  }

  if (!formData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = '请输入有效的邮箱地址';
  }

  if (!formData.subject || formData.subject.trim().length < 3) {
    errors.subject = '主题至少需要3个字符';
  }

  if (!formData.message || formData.message.trim().length < 10) {
    errors.message = '消息内容至少需要10个字符';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
