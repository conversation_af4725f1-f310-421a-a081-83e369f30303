import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';

const Card = styled(motion.div)`
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.07);
  transition: all 0.3s ease;
  height: 100%;
  
  &:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
`;

const ImageContainer = styled.div`
  position: relative;
  overflow: hidden;
  aspect-ratio: 16 / 12;
  max-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f5fb;
  border-bottom: 1px solid #eaeaea;
`;

const Image = styled.img`
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  transition: transform 0.5s ease;
  padding: 10px;
`;

const ImageWrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SlideImage = styled.img`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  margin: auto;
  object-fit: contain;
  opacity: ${props => props.$active ? 1 : 0};
  transition: opacity 0.8s ease;
  padding: 10px;
`;

const SlideControls = styled.div`
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 10px;
  z-index: 10;
`;

const SlideDot = styled.div`
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: ${props => props.$active ? '#ffffff' : 'rgba(255, 255, 255, 0.5)'};
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: #ffffff;
    transform: scale(1.2);
  }
`;

const SlideButton = styled.button`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.4);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0.7;
  transition: opacity 0.3s ease, background-color 0.3s ease;
  font-size: 18px;
  
  ${ImageContainer}:hover & {
    opacity: 1;
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.6);
    transform: translateY(-50%) scale(1.1);
  }
  
  &.prev {
    left: 15px;
  }
  
  &.next {
    right: 15px;
  }
`;

const Content = styled.div`
  padding: 1.8rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
`;

const TitleContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
`;

const Title = styled.h3`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 1.4rem;
  color: #333;
  margin: 0;
  line-height: 1.3;
  flex: 1;
`;

const LinksContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  flex-shrink: 0;
`;

const LinkButton = styled.a`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  background: linear-gradient(135deg, #AED6F1 0%, #85C1E9 100%);
  color: #2C3E50;
  font-size: 0.75rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid transparent;
  white-space: nowrap;
  
  &:hover {
    background: linear-gradient(135deg, #5DADE2 0%, #3498DB 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(52, 152, 219, 0.3);
  }
  
  &.demo {
    background: linear-gradient(135deg, #A9DFBF 0%, #82E0AA 100%);
    
    &:hover {
      background: linear-gradient(135deg, #58D68D 0%, #28B463 100%);
      color: white;
      box-shadow: 0 3px 8px rgba(40, 180, 99, 0.3);
    }
  }

  &.detail {
    background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
    
    &:hover {
      background: linear-gradient(135deg, #BA68C8 0%, #9C27B0 100%);
      color: white;
      box-shadow: 0 3px 8px rgba(156, 39, 176, 0.3);
    }
  }
`;

const LinkIcon = styled.span`
  font-size: 0.8rem;
`;

const Description = styled.p`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.2rem;
  flex-grow: 1;
  
  strong {
    font-weight: bold;
    color: #3498DB;
  }
`;

const TechStack = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin-top: auto;
`;

const TechTag = styled.span`
  font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
  background-color: #f0f7ff;
  color: #4a8eff;
  font-size: 0.85rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  display: inline-block;
  font-weight: 500;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #e0f0ff;
    transform: translateY(-2px);
  }
`;

// 高亮描述文本中的关键词
const highlightText = (text, terms) => {
  if (!terms || terms.length === 0) return text;
  
  let result = text;
  // 按长度降序排序，确保先替换较长的术语
  const sortedTerms = [...terms].sort((a, b) => b.length - a.length);
  
  sortedTerms.forEach(term => {
    // 对term中的特殊字符进行转义
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    
    // 根据术语类型选择不同的匹配策略
    let regex;
    
    // 1. 处理百分比数字
    if (/\d+%$/.test(term)) {
      regex = new RegExp(escapedTerm, 'g');
      // 执行替换
      result = result.replace(regex, `<strong>${term}</strong>`);
    } 
    // 2. 处理带+号的数字
    else if (/\d+\+$/.test(term)) {
      regex = new RegExp(escapedTerm, 'g');
      // 执行替换
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
    // 3. 处理纯数字
    else if (/^\d+$/.test(term)) {
      // 数字前后需要非数字字符或字符串边界
      regex = new RegExp(`(^|[^\\d])${escapedTerm}([^\\d]|$)`, 'g');
      // 替换时保留前后字符
      result = result.replace(regex, (match, p1, p2) => `${p1}<strong>${term}</strong>${p2}`);
      // 修复错误：使用 return 替代 continue
      return;
    }
    // 4. 处理中文技术名称和术语
    else if (/[\u4e00-\u9fa5]/.test(term)) {
      regex = new RegExp(escapedTerm, 'g');
      // 执行替换
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
    // 5. 英文术语使用单词边界
    else {
      regex = new RegExp(`\\b${escapedTerm}\\b`, 'g');
      // 执行替换
      result = result.replace(regex, `<strong>${term}</strong>`);
    }
  });
  
  return result;
};

const ProjectCard = ({ project }) => {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [prevImageIndex, setPrevImageIndex] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imagesPreloaded, setImagesPreloaded] = useState(false);
  const images = project.galleryImages || [project.image];
  
  // 处理描述文本，高亮关键词
  const highlightedDescription = project.highlightTerms 
    ? highlightText(project.description, project.highlightTerms)
    : project.description;
  
  // 预加载所有图片
  useEffect(() => {
    if (images.length <= 1 || imagesPreloaded) return;
    
    const preloadImages = async () => {
      // 检查是否在浏览器环境中
      if (typeof window === 'undefined') {
        setImagesPreloaded(true);
        setImageLoaded(true);
        return;
      }
      
      const imagePromises = images.map((src) => {
        return new Promise((resolve, reject) => {
          const img = new window.Image();
          img.src = src;
          img.onload = resolve;
          img.onerror = reject;
        });
      });
      
      try {
        await Promise.all(imagePromises);
        setImagesPreloaded(true);
        setImageLoaded(true);
      } catch (error) {
        console.error('图片预加载失败', error);
        // 即使部分图片加载失败，也标记为预加载完成，避免阻塞界面
        setImagesPreloaded(true);
        setImageLoaded(true);
      }
    };
    
    preloadImages();
  }, [images, imagesPreloaded]);
  
  useEffect(() => {
    if (imagesPreloaded) {
      // 如果图片已预加载，不需要重置imageLoaded状态
      return;
    }
    setImageLoaded(false);
  }, [currentImageIndex, imagesPreloaded]);
  
  useEffect(() => {
    if (images.length <= 1) return;
    
    const interval = setInterval(() => {
      setPrevImageIndex(currentImageIndex);
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
    }, 4000);
    
    return () => clearInterval(interval);
  }, [images.length, currentImageIndex]);
  
  const handleImageLoad = () => {
    setImageLoaded(true);
  };
  
  const goToPrevImage = (e) => {
    e.stopPropagation();
    setPrevImageIndex(currentImageIndex);
    setCurrentImageIndex((prevIndex) => (prevIndex - 1 + images.length) % images.length);
  };
  
  const goToNextImage = (e) => {
    e.stopPropagation();
    setPrevImageIndex(currentImageIndex);
    setCurrentImageIndex((prevIndex) => (prevIndex + 1) % images.length);
  };
  
  const handleDotClick = (index) => {
    if (index === currentImageIndex) return;
    setPrevImageIndex(currentImageIndex);
    setCurrentImageIndex(index);
  };
  
  return (
    <Card
      whileHover={{ translateY: -5 }}
      transition={{ duration: 0.3 }}
    >
      <ImageContainer>
        <ImageWrapper>
          {/* 显示所有图片，但只有当前图片是可见的 */}
          {images.map((src, index) => (
            <SlideImage
              key={index}
              src={src}
              alt={`${project.title} - ${index + 1}`}
              $active={index === currentImageIndex}
              onLoad={() => {
                if (index === currentImageIndex) {
                  setImageLoaded(true);
                }
                // 当所有图片加载完成时标记预加载完成
                if (index === images.length - 1) {
                  setImagesPreloaded(true);
                }
              }}
            />
          ))}
        </ImageWrapper>
        
        {images.length > 1 && (
          <>
            <SlideButton className="prev" onClick={goToPrevImage}>
              &lsaquo;
            </SlideButton>
            <SlideButton className="next" onClick={goToNextImage}>
              &rsaquo;
            </SlideButton>
            <SlideControls>
              {images.map((_, index) => (
                <SlideDot 
                  key={index} 
                  $active={index === currentImageIndex}
                  onClick={() => handleDotClick(index)}
                />
              ))}
            </SlideControls>
          </>
        )}
      </ImageContainer>
      <Content>
        <TitleContainer>
          <Title>{project.title}</Title>
          {/* 项目链接区域 - 移动到标题右侧 */}
          <LinksContainer>
            {project.liveUrl && (
              <LinkButton 
                href={project.liveUrl} 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <LinkIcon>🌏</LinkIcon>
                <div style={{fontSize: '0.8rem', color: 'rgb(45, 45, 45)'}}>访问</div>
              </LinkButton>
            )}
            {project.demoInfo && (
              <LinkButton 
                className="demo"
                onClick={(e) => {
                  e.preventDefault();
                  // 复制到剪贴板
                  if (navigator.clipboard) {
                    navigator.clipboard.writeText(project.demoInfo).then(() => {
                      alert(`已复制到剪贴板：${project.demoInfo}`);
                    }).catch(() => {
                      alert(`体验方式：${project.demoInfo}`);
                    });
                  } else {
                    alert(`体验方式：${project.demoInfo}`);
                  }
                }}
              >
                <LinkIcon>📱</LinkIcon>
                <div style={{fontSize: '0.8rem', color: 'rgb(45, 45, 45)'}}>体验</div>
              </LinkButton>
            )}
            {project.id === 3 && (
              <LinkButton 
                className="detail"
                onClick={(e) => {
                  e.preventDefault();
                  navigate(`/project/${project.id}`);
                }}
              >
                <LinkIcon>📋</LinkIcon>
                <div style={{fontSize: '0.8rem', color: 'rgb(45, 45, 45)'}}>详情</div>
              </LinkButton>
            )}
          </LinksContainer>
        </TitleContainer>
        
        <Description dangerouslySetInnerHTML={{ __html: highlightedDescription }} />
        
        <TechStack>
          {project.techStack && project.techStack.map((tech, index) => (
            <TechTag key={index}>{tech}</TechTag>
          ))}
        </TechStack>
      </Content>
    </Card>
  );
};

export default ProjectCard; 