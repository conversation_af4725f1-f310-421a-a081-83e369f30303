import React, { useState } from 'react';
import { sendEmail, validateFormData } from '../services/emailService';

const EmailTest = () => {
  const [testResult, setTestResult] = useState('');

  const testEmailService = async () => {
    setTestResult('正在测试EmailJS配置...');
    
    // 测试数据
    const testData = {
      name: '测试用户',
      email: '<EMAIL>',
      subject: 'EmailJS测试邮件',
      message: '这是一封测试邮件，用于验证EmailJS配置是否正确。'
    };

    try {
      // 验证表单数据
      const validation = validateFormData(testData);
      if (!validation.isValid) {
        setTestResult(`验证失败: ${JSON.stringify(validation.errors)}`);
        return;
      }

      // 发送测试邮件
      const result = await sendEmail(testData);
      
      if (result.success) {
        setTestResult('✅ EmailJS配置正确！测试邮件发送成功。');
      } else {
        setTestResult(`❌ 发送失败: ${result.message}`);
      }
    } catch (error) {
      setTestResult(`❌ 测试失败: ${error.message}`);
    }
  };

  const checkConfig = () => {
    const config = {
      SERVICE_ID: import.meta.env.VITE_EMAILJS_SERVICE_ID,
      TEMPLATE_ID: import.meta.env.VITE_EMAILJS_TEMPLATE_ID,
      PUBLIC_KEY: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
    };

    const missing = Object.entries(config)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missing.length > 0) {
      setTestResult(`❌ 缺少环境变量: ${missing.join(', ')}`);
    } else {
      setTestResult('✅ 所有环境变量已配置');
    }
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      padding: '20px', 
      border: '1px solid #ccc',
      borderRadius: '8px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h3>EmailJS 测试工具</h3>
      <button 
        onClick={checkConfig}
        style={{ 
          margin: '5px', 
          padding: '8px 12px', 
          background: '#007bff', 
          color: 'white', 
          border: 'none', 
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        检查配置
      </button>
      <button 
        onClick={testEmailService}
        style={{ 
          margin: '5px', 
          padding: '8px 12px', 
          background: '#28a745', 
          color: 'white', 
          border: 'none', 
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        发送测试邮件
      </button>
      {testResult && (
        <div style={{ 
          marginTop: '10px', 
          padding: '10px', 
          background: '#f8f9fa', 
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          fontSize: '14px',
          whiteSpace: 'pre-wrap'
        }}>
          {testResult}
        </div>
      )}
    </div>
  );
};

export default EmailTest;
