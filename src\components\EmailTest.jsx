import React, { useState } from 'react';
import { sendEmail, validateFormData } from '../services/emailService';

const EmailTest = () => {
  const [testResult, setTestResult] = useState('');

  const testEmailService = async () => {
    setTestResult('正在测试EmailJS配置...');
    
    // 测试数据
    const testData = {
      name: '测试用户',
      email: '<EMAIL>',
      subject: 'EmailJS测试邮件',
      message: '这是一封测试邮件，用于验证EmailJS配置是否正确。如果您收到这封邮件，说明配置成功！'
    };

    try {
      console.log('开始测试邮件发送...');
      
      // 验证表单数据
      const validation = validateFormData(testData);
      if (!validation.isValid) {
        setTestResult(`验证失败: ${JSON.stringify(validation.errors)}`);
        return;
      }

      console.log('表单验证通过，开始发送邮件...');

      // 发送测试邮件
      const result = await sendEmail(testData);
      
      console.log('邮件发送结果:', result);
      
      if (result.success) {
        setTestResult('✅ EmailJS配置正确！测试邮件发送成功。\n请检查您的邮箱 <EMAIL>');
      } else {
        setTestResult(`❌ 发送失败: ${result.message}\n错误详情: ${JSON.stringify(result.error, null, 2)}`);
      }
    } catch (error) {
      console.error('测试失败:', error);
      setTestResult(`❌ 测试失败: ${error.message}\n${error.stack}`);
    }
  };

  const checkConfig = () => {
    const config = {
      SERVICE_ID: import.meta.env.VITE_EMAILJS_SERVICE_ID,
      TEMPLATE_ID: import.meta.env.VITE_EMAILJS_TEMPLATE_ID,
      PUBLIC_KEY: import.meta.env.VITE_EMAILJS_PUBLIC_KEY,
    };

    const missing = Object.entries(config)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    let result = '📋 环境变量检查:\n';
    result += `SERVICE_ID: ${config.SERVICE_ID || '❌ 未设置'}\n`;
    result += `TEMPLATE_ID: ${config.TEMPLATE_ID || '❌ 未设置'}\n`;
    result += `PUBLIC_KEY: ${config.PUBLIC_KEY ? '✅ 已设置' : '❌ 未设置'}\n\n`;

    if (missing.length > 0) {
      result += `❌ 缺少环境变量: ${missing.join(', ')}`;
    } else {
      result += '✅ 所有环境变量已配置';
    }

    setTestResult(result);
  };

  const checkEmailJSConnection = () => {
    setTestResult('🔍 检查EmailJS连接状态...');
    
    try {
      // 检查EmailJS是否正确加载
      if (typeof window !== 'undefined' && window.emailjs) {
        setTestResult('✅ EmailJS库已加载\n✅ 可以尝试发送邮件');
      } else {
        setTestResult('❌ EmailJS库未正确加载\n请检查网络连接或重新安装依赖');
      }
    } catch (error) {
      setTestResult(`❌ 检查失败: ${error.message}`);
    }
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      padding: '20px', 
      border: '1px solid #ccc',
      borderRadius: '8px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      zIndex: 9999,
      maxWidth: '350px',
      fontSize: '14px'
    }}>
      <h3 style={{ margin: '0 0 15px 0' }}>EmailJS 测试工具</h3>
      
      <div style={{ marginBottom: '10px' }}>
        <button 
          onClick={checkConfig}
          style={{ 
            margin: '2px', 
            padding: '6px 10px', 
            background: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          检查配置
        </button>
        
        <button 
          onClick={checkEmailJSConnection}
          style={{ 
            margin: '2px', 
            padding: '6px 10px', 
            background: '#6f42c1', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          检查连接
        </button>
        
        <button 
          onClick={testEmailService}
          style={{ 
            margin: '2px', 
            padding: '6px 10px', 
            background: '#28a745', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          发送测试邮件
        </button>
      </div>
      
      {testResult && (
        <div style={{ 
          marginTop: '10px', 
          padding: '10px', 
          background: '#f8f9fa', 
          border: '1px solid #dee2e6',
          borderRadius: '4px',
          fontSize: '12px',
          whiteSpace: 'pre-wrap',
          maxHeight: '300px',
          overflow: 'auto'
        }}>
          {testResult}
        </div>
      )}
    </div>
  );
};

export default EmailTest;
