# EmailJS 邮件模板配置指南

## 邮件模板设置

在EmailJS控制台创建邮件模板时，请使用以下配置：

### 模板主题 (Subject)
```
来自{{from_name}}的新消息：{{subject}}
```

### 模板内容 (Content)
```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>来自个人网站的新消息</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: white;
            padding: 20px;
            border-radius: 8px 8px 0 0;
            text-align: center;
        }
        .content {
            background: #f9f9f9;
            padding: 20px;
            border: 1px solid #ddd;
        }
        .footer {
            background: #333;
            color: white;
            padding: 15px;
            border-radius: 0 0 8px 8px;
            text-align: center;
            font-size: 14px;
        }
        .info-row {
            margin: 10px 0;
            padding: 8px;
            background: white;
            border-left: 4px solid #3B82F6;
        }
        .message-content {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎉 新的联系消息</h1>
        <p>您的个人网站收到了一条新消息</p>
    </div>
    
    <div class="content">
        <div class="info-row">
            <strong>发送者姓名：</strong> {{from_name}}
        </div>
        
        <div class="info-row">
            <strong>发送者邮箱：</strong> {{from_email}}
        </div>
        
        <div class="info-row">
            <strong>消息主题：</strong> {{subject}}
        </div>
        
        <div class="message-content">
            <h3>消息内容：</h3>
            <p>{{message}}</p>
        </div>
        
        <div class="info-row">
            <strong>发送时间：</strong> {{sent_at}}
        </div>
    </div>
    
    <div class="footer">
        <p>此邮件由您的个人网站自动发送</p>
        <p>回复邮箱：{{reply_to}}</p>
    </div>
</body>
</html>
```

## 模板变量说明

在EmailJS模板中，以下变量会被自动替换：

- `{{from_name}}` - 发送者姓名
- `{{from_email}}` - 发送者邮箱
- `{{subject}}` - 邮件主题
- `{{message}}` - 邮件内容
- `{{to_name}}` - 接收者姓名（固定为"Tully"）
- `{{reply_to}}` - 回复邮箱地址
- `{{sent_at}}` - 发送时间（自动生成）

## 配置步骤

1. 登录 https://dashboard.emailjs.com/admin
2. 创建新的邮件模板
3. 复制上述主题和内容
4. 保存模板并获取Template ID
5. 将Template ID填入 `.env` 文件中的 `VITE_EMAILJS_TEMPLATE_ID`

## 测试建议

建议先使用EmailJS的测试功能验证模板是否正常工作，然后再在网站上进行实际测试。
