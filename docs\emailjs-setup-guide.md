# EmailJS 完整配置指南

## 📧 EmailJS邮件发送功能完整实现步骤

### 第一步：EmailJS账户设置

1. **访问EmailJS控制台**
   - 打开 https://dashboard.emailjs.com/admin
   - 使用Google账户或邮箱注册/登录

2. **创建邮件服务 (Email Service)**
   - 点击左侧菜单 "Email Services"
   - 点击 "Add New Service"
   - 选择 "Gmail" (推荐) 或其他邮件提供商
   - 点击 "Connect Account" 并授权您的Gmail账户
   - 记录生成的 **Service ID**

3. **创建邮件模板 (Email Template)**
   - 点击左侧菜单 "Email Templates"
   - 点击 "Create New Template"
   - 模板名称：`contact_form_template`
   - 参考 `docs/emailjs-template-guide.md` 设置模板内容
   - 保存并记录生成的 **Template ID**

4. **获取公钥 (Public Key)**
   - 点击左侧菜单 "Account"
   - 在 "API Keys" 部分找到 **Public Key**
   - 复制此密钥

### 第二步：配置环境变量

1. **编辑 `.env` 文件**
   ```env
   # EmailJS配置
   VITE_EMAILJS_SERVICE_ID=service_xxxxxxx
   VITE_EMAILJS_TEMPLATE_ID=template_xxxxxxx
   VITE_EMAILJS_PUBLIC_KEY=xxxxxxxxxxxxxxxx
   ```

2. **替换实际值**
   - 将 `service_xxxxxxx` 替换为您的Service ID
   - 将 `template_xxxxxxx` 替换为您的Template ID
   - 将 `xxxxxxxxxxxxxxxx` 替换为您的Public Key

### 第三步：重启开发服务器

```bash
# 停止当前服务器 (Ctrl+C)
# 重新启动以加载环境变量
npm run dev
```

### 第四步：测试邮件功能

1. **访问联系页面**
   - 打开 http://localhost:3000/#contact
   - 填写测试表单

2. **验证功能**
   - 表单验证是否正常工作
   - 提交后是否显示"发送中..."状态
   - 是否收到成功/失败提示
   - 检查您的邮箱是否收到邮件

### 第五步：故障排除

#### 常见问题及解决方案

1. **邮件发送失败**
   - 检查 `.env` 文件中的配置是否正确
   - 确认EmailJS服务状态是否正常
   - 查看浏览器控制台是否有错误信息

2. **环境变量未生效**
   - 确保 `.env` 文件在项目根目录
   - 重启开发服务器
   - 检查变量名是否以 `VITE_` 开头

3. **模板变量未替换**
   - 检查模板中的变量名是否与代码中一致
   - 确认模板保存成功

4. **Gmail授权问题**
   - 确保Gmail账户已启用"不够安全的应用访问权限"
   - 或使用应用专用密码

### 第六步：生产环境配置

1. **Netlify部署**
   - 在Netlify控制台的环境变量中添加EmailJS配置
   - 变量名保持与 `.env` 文件一致

2. **Vercel部署**
   - 在Vercel项目设置中添加环境变量
   - 重新部署项目

### 安全注意事项

1. **不要提交 `.env` 文件到Git**
   - 确保 `.env` 在 `.gitignore` 中
   - 公钥可以暴露，但服务ID和模板ID建议保密

2. **EmailJS限制**
   - 免费账户每月有200封邮件限制
   - 考虑升级到付费计划以获得更多配额

### 功能特性

✅ **已实现的功能**
- 表单数据验证
- 实时错误提示
- 发送状态显示
- 成功/失败反馈
- 防重复提交
- 响应式设计

✅ **邮件模板功能**
- 美观的HTML邮件格式
- 发送者信息显示
- 自动回复地址设置
- 发送时间记录

## 🎉 完成！

现在您的个人网站已经具备了完整的邮件发送功能。访问者可以通过联系表单直接向您发送邮件，您将在Gmail中收到格式化的邮件通知。
